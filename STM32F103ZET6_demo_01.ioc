#MicroXplorer Configuration settings - do not modify
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SYS
Mcu.IP3=USART2
Mcu.IPNb=4
Mcu.Name=STM32F103Z(C-D-E)Tx
Mcu.Package=LQFP144
Mcu.Pin0=OSC_IN
Mcu.Pin1=OSC_OUT
Mcu.Pin10=VP_SYS_VS_ND
Mcu.Pin11=VP_SYS_VS_Systick
Mcu.Pin2=PA4
Mcu.Pin3=PA5
Mcu.Pin4=PA6
Mcu.Pin5=PD5
Mcu.Pin6=PD6
Mcu.Pin7=PB9
Mcu.Pin8=PE0
Mcu.Pin9=PE1
Mcu.PinsNb=12
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103ZETx
MxCube.Version=6.2.0
MxDb.Version=DB.6.0.20
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false
OSC_IN.Mode=HSE-External-Oscillator
OSC_IN.Signal=RCC_OSC_IN
OSC_OUT.Mode=HSE-External-Oscillator
OSC_OUT.Signal=RCC_OSC_OUT
PA4.GPIOParameters=PinState,GPIO_Label
PA4.GPIO_Label=EN_y
PA4.Locked=true
PA4.PinState=GPIO_PIN_SET
PA4.Signal=GPIO_Output
PA5.GPIOParameters=PinState,GPIO_Label
PA5.GPIO_Label=PUL_y
PA5.Locked=true
PA5.PinState=GPIO_PIN_SET
PA5.Signal=GPIO_Output
PA6.GPIOParameters=PinState,GPIO_Label
PA6.GPIO_Label=DIR_y
PA6.Locked=true
PA6.PinState=GPIO_PIN_SET
PA6.Signal=GPIO_Output
PB9.GPIOParameters=PinState,GPIO_Label
PB9.GPIO_Label=PUL_x
PB9.Locked=true
PB9.PinState=GPIO_PIN_SET
PB9.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_Speed,GPIO_Mode
PD5.GPIO_Mode=GPIO_MODE_AF_PP
PD5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD5.Locked=true
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.GPIOParameters=GPIO_PuPd
PD6.GPIO_PuPd=GPIO_NOPULL
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PE0.GPIOParameters=PinState,GPIO_Label
PE0.GPIO_Label=EN_x
PE0.Locked=true
PE0.PinState=GPIO_PIN_SET
PE0.Signal=GPIO_Output
PE1.GPIOParameters=PinState,GPIO_Label
PE1.GPIO_Label=DIR_y
PE1.Locked=true
PE1.PinState=GPIO_PIN_SET
PE1.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103ZETx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=STM32F103ZET6_demo_01.ioc
ProjectManager.ProjectName=STM32F103ZET6_demo_01
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FSMCFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.I2S2Freq_Value=72000000
RCC.I2S3Freq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FSMCFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SDIOFreq_Value=72000000
RCC.SDIOHCLKDiv2FreqValue=36000000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
VP_SYS_VS_ND.Mode=No_Debug
VP_SYS_VS_ND.Signal=SYS_VS_ND
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
