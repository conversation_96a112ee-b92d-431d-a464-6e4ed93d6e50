[1/26] CC build/__Core_Src_gpio.o
[2/26] CC build/__Core_Src_main.o
[3/26] CC build/__Core_Src_stm32f1xx_hal_msp.o
[4/26] CC build/__Core_Src_stm32f1xx_it.o
[5/26] CC build/__Core_Src_system_stm32f1xx.o
[6/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal.o
[7/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_cortex.o
[8/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_dma.o
[9/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_exti.o
[10/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash.o
[11/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_flash_ex.o
[12/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio.o
[13/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_gpio_ex.o
[14/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_pwr.o
[15/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc.o
[16/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_rcc_ex.o
[17/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim.o
[18/26] CC build/__Drivers_STM32F1xx_HAL_Driver_Src_stm32f1xx_hal_tim_ex.o
[19/26] CC build/__MyApp_btn_app.o
[20/26] CC build/__MyApp_key_app.o
[21/26] CC build/__MyApp_led_app.o
[22/26] CC build/__MyApp_pid_app.o
[23/26] CC build/__MyApp_scheduler.o
[24/26] LINK build/STM32F103ZET6_demo_01.elf
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-closer.o): in function `_close_r':
(.text._close_r+0xc): warning: _close is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-closer.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-lseekr.o): in function `_lseek_r':
(.text._lseek_r+0x10): warning: _lseek is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-lseekr.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-readr.o): in function `_read_r':
(.text._read_r+0x10): warning: _read is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-readr.o): note: the message above does not take linker garbage collection into account
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-writer.o): in function `_write_r':
(.text._write_r+0x10): warning: _write is not implemented and will always fail
D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld.exe: D:/STM32CubeCLT_1.18.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp\libc_nano.a(libc_a-writer.o): note: the message above does not take linker garbage collection into account
Memory region         Used Size  Region Size  %age Used
           FLASH:        4648 B       512 KB      0.89%
             RAM:        1040 B       128 KB      0.79%
[25/26] HEX build/STM32F103ZET6_demo_01.hex
[26/26] SIZE build/STM32F103ZET6_demo_01.elf
   text	   data	    bss	    dec	    hex	filename
   4636	     12	   1028	   5676	   162c	build/STM32F103ZET6_demo_01.elf

